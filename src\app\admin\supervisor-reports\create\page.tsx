'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Save, ArrowLeft, FileText, Eye, Printer } from 'lucide-react';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';
import { cn } from '@/utils/cn';
import LiteraryReportEditor from '@/components/supervisor-reports/LiteraryReportEditor';
import SimpleLiteraryEditor from '@/components/supervisor-reports/SimpleLiteraryEditor';
import FinancialTableEditor from '@/components/supervisor-reports/FinancialTableEditor';
import ReportExporter from '@/components/supervisor-reports/ReportExporter';
import ValidationStatus from '@/components/supervisor-reports/ValidationStatus';
import { validateReport, formatValidationMessage, getValidationSummary } from '@/utils/reportValidation';

interface FinancialRow {
  id: string;
  category: string;
  description: string;
  amount: number;
  type: 'income' | 'expense';
}

export default function CreateSupervisorReportPage() {
  const router = useRouter();
  
  // بيانات التقرير الأساسية
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [periodStart, setPeriodStart] = useState<Date>(new Date());
  const [periodEnd, setPeriodEnd] = useState<Date>(new Date());
  
  // محتوى التقرير الأدبي
  const [literaryContent, setLiteraryContent] = useState('');
  
  // بيانات التقرير المالي
  const [financialData, setFinancialData] = useState<FinancialRow[]>([
    { id: '1', category: 'المداخيل', description: 'رسوم التسجيل', amount: 0, type: 'income' },
    { id: '2', category: 'المداخيل', description: 'التبرعات', amount: 0, type: 'income' },
    { id: '3', category: 'المصاريف', description: 'رواتب المعلمين', amount: 0, type: 'expense' },
    { id: '4', category: 'المصاريف', description: 'مصاريف إدارية', amount: 0, type: 'expense' },
  ]);
  
  // حالات التحميل والحفظ
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [useSimpleEditor, setUseSimpleEditor] = useState(true); // استخدام المحرر المبسط افتراضياً

  // تحميل البيانات التلقائية للتقرير الأدبي
  useEffect(() => {
    loadLiteraryData();
  }, [periodStart, periodEnd]);

  const loadLiteraryData = async () => {
    setIsLoading(true);
    try {
      // جلب البيانات من API التقرير الأدبي
      const response = await fetch(
        `/api/reports/literary?startDate=${periodStart.toISOString()}&endDate=${periodEnd.toISOString()}`
      );
      
      if (response.ok) {
        const data = await response.json();
        // تحويل البيانات إلى محتوى HTML
        const htmlContent = generateLiteraryHTML(data.data);
        setLiteraryContent(htmlContent);
      }
    } catch (error) {
      console.error('خطأ في تحميل البيانات الأدبية:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const generateLiteraryHTML = (data: any) => {
    return `
      <div class="literary-report">
        <h2>📊 الإحصائيات العامة</h2>
        <ul>
          <li>إجمالي الطلاب: ${data.generalStats?.totalStudents || 0}</li>
          <li>إجمالي المعلمين: ${data.generalStats?.totalTeachers || 0}</li>
          <li>إجمالي الأقسام: ${data.generalStats?.totalClasses || 0}</li>
          <li>إجمالي الحفاظ: ${data.generalStats?.totalMemorizers || 0}</li>
        </ul>
        
        <h2>👥 تفاصيل الطلاب</h2>
        <p>العدد الإجمالي للطلاب المسجلين: ${data.studentsDetails?.total || 0}</p>
        
        <h2>📖 تقدم حفظ القرآن الكريم</h2>
        <p>متوسط التقدم في الحفظ: ${data.quranProgress?.averageProgress || 0}%</p>
        
        <h2>🎯 الأنشطة والفعاليات</h2>
        <p>عدد الأنشطة المنجزة: ${data.activitiesDetails?.total || 0}</p>
        
        <h2>📚 الدورات التدريبية</h2>
        <p>عدد الدورات المقامة: ${data.trainingCourses?.total || 0}</p>
      </div>
    `;
  };

  // حساب المجاميع للمعاينة
  const totalIncome = financialData
    .filter(row => row.type === 'income')
    .reduce((sum, row) => sum + row.amount, 0);

  const totalExpenses = financialData
    .filter(row => row.type === 'expense')
    .reduce((sum, row) => sum + row.amount, 0);

  const balance = totalIncome - totalExpenses;

  // التحقق من صحة البيانات
  const validateForm = () => {
    const reportData = {
      title,
      description,
      periodStart,
      periodEnd,
      literaryContent,
      financialData
    };

    const validationResult = validateReport(reportData);

    // تحويل نتائج التحقق إلى تنسيق الأخطاء المحلي
    const newErrors: Record<string, string> = {};

    validationResult.errors.forEach(error => {
      newErrors[error.field] = error.message;
    });

    // عرض التحذيرات في وحدة التحكم
    if (validationResult.warnings.length > 0) {
      console.warn('تحذيرات التحقق:', validationResult.warnings.map(w => w.message));
    }

    setErrors(newErrors);
    return validationResult.isValid;
  };

  // حفظ التقرير
  const handleSave = async (status: 'DRAFT' | 'PUBLISHED' = 'DRAFT') => {
    if (!validateForm()) return;
    
    setIsSaving(true);
    try {
      const response = await fetch('/api/supervisor-reports', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title,
          description,
          periodStart: periodStart.toISOString(),
          periodEnd: periodEnd.toISOString(),
          literaryContent,
          financialData,
          status
        }),
      });

      if (response.ok) {
        const result = await response.json();
        router.push('/admin/supervisor-reports');
      } else {
        console.error('فشل في حفظ التقرير');
      }
    } catch (error) {
      console.error('خطأ في حفظ التقرير:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // معاينة التقرير
  const handlePreview = () => {
    // فتح نافذة معاينة
    const previewWindow = window.open('', '_blank');
    if (previewWindow) {
      previewWindow.document.write(generatePreviewHTML());
    }
  };

  const generatePreviewHTML = () => {
    return `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <title>${title}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
          .header { text-align: center; margin-bottom: 30px; }
          .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
          .financial-table { width: 100%; border-collapse: collapse; }
          .financial-table th, .financial-table td { border: 1px solid #ddd; padding: 8px; text-align: center; }
          .financial-table th { background-color: #f5f5f5; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>${title}</h1>
          <p>الفترة: من ${format(periodStart, 'PPP', { locale: ar })} إلى ${format(periodEnd, 'PPP', { locale: ar })}</p>
        </div>
        
        <div class="section">
          <h2>📘 التقرير الأدبي</h2>
          ${literaryContent}
        </div>
        
        <div class="section">
          <h2>💰 التقرير المالي</h2>
          <table class="financial-table">
            <thead>
              <tr><th>الفئة</th><th>البيان</th><th>المبلغ (دج)</th></tr>
            </thead>
            <tbody>
              ${financialData.map(row => `
                <tr>
                  <td>${row.category}</td>
                  <td>${row.description}</td>
                  <td>${row.amount.toLocaleString()}</td>
                </tr>
              `).join('')}
              <tr style="font-weight: bold; background-color: #f0f0f0;">
                <td colspan="2">إجمالي المداخيل</td>
                <td>${totalIncome.toLocaleString()}</td>
              </tr>
              <tr style="font-weight: bold; background-color: #f0f0f0;">
                <td colspan="2">إجمالي المصاريف</td>
                <td>${totalExpenses.toLocaleString()}</td>
              </tr>
              <tr style="font-weight: bold; background-color: ${balance >= 0 ? '#d4edda' : '#f8d7da'};">
                <td colspan="2">الرصيد النهائي</td>
                <td>${balance.toLocaleString()}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </body>
      </html>
    `;
  };

  return (
    <div className="container mx-auto p-6 space-y-6" dir="rtl">
      {/* الرأس */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
            <FileText className="h-8 w-8 text-primary" />
            إنشاء تقرير موحد جديد
          </h1>
          <p className="text-gray-600 mt-1">
            إنشاء تقرير يجمع بين التقرير الأدبي والمالي
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            رجوع
          </Button>
        </div>
      </div>

      {/* معلومات التقرير الأساسية */}
      <Card>
        <CardHeader>
          <CardTitle>معلومات التقرير الأساسية</CardTitle>
          <CardDescription>
            أدخل المعلومات الأساسية للتقرير والفترة الزمنية
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="title">عنوان التقرير *</Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="مثال: التقرير الشهري لشهر يناير 2024"
                className={errors.title ? 'border-red-500' : ''}
              />
              {errors.title && (
                <p className="text-sm text-red-500">{errors.title}</p>
              )}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="description">وصف التقرير</Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="وصف مختصر للتقرير..."
                rows={3}
              />
            </div>
          </div>

          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>تاريخ بداية الفترة *</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !periodStart && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {periodStart ? format(periodStart, "PPP", { locale: ar }) : "اختر التاريخ"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={periodStart}
                    onSelect={(date) => date && setPeriodStart(date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-2">
              <Label>تاريخ نهاية الفترة *</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !periodEnd && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {periodEnd ? format(periodEnd, "PPP", { locale: ar }) : "اختر التاريخ"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={periodEnd}
                    onSelect={(date) => date && setPeriodEnd(date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          {errors.period && (
            <p className="text-sm text-red-500">{errors.period}</p>
          )}
        </CardContent>
      </Card>

      {/* القسم الأدبي */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5 text-blue-600" />
                📘 التقرير الأدبي
              </CardTitle>
              <CardDescription>
                اختر نوع المحرر المناسب لك
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant={useSimpleEditor ? 'default' : 'outline'}
                size="sm"
                onClick={() => setUseSimpleEditor(true)}
              >
                محرر مبسط
              </Button>
              <Button
                variant={!useSimpleEditor ? 'default' : 'outline'}
                size="sm"
                onClick={() => setUseSimpleEditor(false)}
              >
                محرر متقدم
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {useSimpleEditor ? (
        <SimpleLiteraryEditor
          value={literaryContent}
          onChange={setLiteraryContent}
          periodStart={periodStart}
          periodEnd={periodEnd}
          isLoading={isLoading}
        />
      ) : (
        <LiteraryReportEditor
          value={literaryContent}
          onChange={setLiteraryContent}
          periodStart={periodStart}
          periodEnd={periodEnd}
          isLoading={isLoading}
        />
      )}

      {/* القسم المالي */}
      <FinancialTableEditor
        data={financialData}
        onChange={setFinancialData}
        periodStart={periodStart}
        periodEnd={periodEnd}
      />

      {/* التحقق من صحة البيانات */}
      <ValidationStatus
        reportData={{
          title,
          description,
          periodStart,
          periodEnd,
          literaryContent,
          financialData
        }}
      />

      {/* معاينة وتصدير التقرير */}
      <ReportExporter
        reportData={{
          title,
          description,
          periodStart,
          periodEnd,
          literaryContent,
          financialData
        }}
        officeSettings={{
          organizationName: 'جمعـية العـلمـاء المسلـميـن الجـزائـرييــــــن',
          officeName: 'المكـــــــتب البلدي',
          presidentName: 'رئيس المكتب البلدي',
          presidentTitle: 'المسؤول عن التقارير'
        }}
      />

      {/* أزرار الحفظ */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button
                onClick={handlePreview}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Eye className="h-4 w-4" />
                معاينة سريعة
              </Button>
            </div>

            <div className="flex items-center gap-2">
              <Button
                onClick={() => handleSave('DRAFT')}
                variant="outline"
                disabled={isSaving}
                className="flex items-center gap-2"
              >
                <Save className="h-4 w-4" />
                حفظ كمسودة
              </Button>
              <Button
                onClick={() => handleSave('PUBLISHED')}
                disabled={isSaving}
                className="flex items-center gap-2"
              >
                <Save className="h-4 w-4" />
                {isSaving ? 'جاري الحفظ...' : 'حفظ ونشر التقرير'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
