'use client';
import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { toast } from 'react-toastify';
// import { useRouter } from 'next/navigation';
import axios from 'axios';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { FaCog, FaImage, FaGlobe, FaList, FaPhone, FaEnvelope, FaFacebook, FaTwitter, FaInstagram, FaYoutube, FaHandHoldingHeart } from 'react-icons/fa';
import { applyColors, saveColors, SiteColors } from '@/utils/simpleColorSystem';
import LogoDiagnostic from '@/components/LogoDiagnostic';
import ColorContrastTester from '@/components/ColorContrastTester';
import ImageTester from '@/components/admin/ImageTester';
import ImagePathFixer from '@/components/admin/ImagePathFixer';
import PageBackgroundsManager from '@/components/admin/PageBackgroundsManager';
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute';

interface SiteSettings {
  siteName: string;
  siteDescription: string;
  logoUrl: string;
  faviconUrl: string;
  primaryColor: string;
  secondaryColor: string;
  sidebarColor: string;
  backgroundColor: string;
  accentColor: string;
  textColor: string;
  registrationEnabled: boolean;
  headerLinks: {
    id: string;
    title: string;
    url: string;
    iconUrl?: string;
    isActive: boolean;
    order: number;
  }[];
  footerLinks: {
    id: string;
    title: string;
    url: string;
    isActive: boolean;
    order: number;
  }[];
  socialLinks: {
    facebook: string;
    twitter: string;
    instagram: string;
    youtube: string;
  };
  contactInfo: {
    email: string;
    phone: string;
    address: string;
    donationInfo?: {
      phone1: string;        // رقم الهاتف الأول للتبرعات
      phone2?: string;       // رقم الهاتف الثاني (اختياري)
      ccpAccount: string;    // حساب CCP
      cpaAccount: string;    // حساب CPA
      bdrAccount: string;    // حساب BDR
      description?: string;  // وصف إضافي للتبرعات (اختياري)
    };
  };
}

const defaultSettings: SiteSettings = {
  siteName: 'نظام برهان للقرآن الكريم',
  siteDescription: 'منصة تعليمية متكاملة لتعليم القرآن الكريم عن بعد',
  logoUrl: '/logo.svg',
  faviconUrl: '/favicon.ico',
  primaryColor: '#169b88',
  secondaryColor: '#1ab19c',
  sidebarColor: '#1a202c',
  backgroundColor: '#f3f4f6',
  accentColor: '#10b981',
  textColor: '#1f2937',
  registrationEnabled: true,
  headerLinks: [
    { id: '1', title: 'الرئيسية', url: '/', isActive: true, order: 1 },
    { id: '2', title: 'من نحن', url: '/about', isActive: true, order: 2 },
    { id: '3', title: 'البرامج', url: '/programs', isActive: true, order: 3 },
    { id: '4', title: 'مجالس الختم', url: '/khatm-sessions', isActive: true, order: 4 },
    { id: '5', title: 'التبرعات', url: '/donations', isActive: true, order: 5 },
    { id: '6', title: 'اتصل بنا', url: '/contact', isActive: true, order: 6 },
  ],
  footerLinks: [
    { id: '1', title: 'الرئيسية', url: '/', isActive: true, order: 1 },
    { id: '2', title: 'من نحن', url: '/about', isActive: true, order: 2 },
    { id: '3', title: 'البرامج', url: '/programs', isActive: true, order: 3 },
    { id: '4', title: 'اتصل بنا', url: '/contact', isActive: true, order: 4 },
    { id: '5', title: 'التبرعات', url: '/donations', isActive: true, order: 5 },
    { id: '6', title: 'التسجيل', url: '/register', isActive: true, order: 6 },
    { id: '7', title: 'تسجيل الدخول', url: '/login', isActive: true, order: 7 },
  ],
  socialLinks: {
    facebook: 'https://facebook.com',
    twitter: 'https://twitter.com',
    instagram: 'https://instagram.com',
    youtube: 'https://youtube.com',
  },
  contactInfo: {
    email: '<EMAIL>',
    phone: '+213 123 456 789',
    address: 'شارع الاستقلال، الجزائر العاصمة، الجزائر',
    donationInfo: {
      phone1: '+213 123 456 789',
      phone2: '+213 987 654 321',
      ccpAccount: '**********',
      cpaAccount: '**********',
      bdrAccount: '**********',
      description: 'يمكنكم التبرع من خلال الحسابات التالية أو التواصل معنا هاتفياً'
    }
  }
};

const AdminSetupPage = () => {
  const [settings, setSettings] = useState<SiteSettings>(defaultSettings);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("general");
  const [newHeaderLink, setNewHeaderLink] = useState({ title: '', url: '' });
  const [newFooterLink, setNewFooterLink] = useState({ title: '', url: '' });
  const [uploadingLogo, setUploadingLogo] = useState(false);
  const [uploadingFavicon, setUploadingFavicon] = useState(false);
  const [showContrastTester, setShowContrastTester] = useState(false);

  // دالة لتحديث الفافيكون فوراً
  const updateFaviconImmediately = (faviconUrl: string) => {
    try {
      const timestamp = Date.now();
      const newFaviconUrl = faviconUrl + '?v=' + timestamp;

      // البحث عن الفافيكون الأساسي وتحديثه
      let mainFavicon = document.querySelector('link[rel="icon"]') as HTMLLinkElement;
      if (mainFavicon) {
        mainFavicon.href = newFaviconUrl;
      } else {
        mainFavicon = document.createElement('link');
        mainFavicon.rel = 'icon';
        mainFavicon.type = 'image/x-icon';
        mainFavicon.href = newFaviconUrl;
        document.head.appendChild(mainFavicon);
      }

      // البحث عن فافيكون 32x32 وتحديثه
      let favicon32 = document.querySelector('link[rel="icon"][sizes="32x32"]') as HTMLLinkElement;
      if (favicon32) {
        favicon32.href = newFaviconUrl;
      } else {
        favicon32 = document.createElement('link');
        favicon32.rel = 'icon';
        favicon32.type = 'image/png';
        favicon32.sizes = '32x32';
        favicon32.href = newFaviconUrl;
        document.head.appendChild(favicon32);
      }

      console.log('Favicon updated immediately to:', faviconUrl);
    } catch (error) {
      console.error('Error updating favicon immediately:', error);
    }
  };

  useEffect(() => {
    // تحميل الإعدادات من localStorage أولاً
    const loadSettingsFromStorage = () => {
      try {
        const savedSettings = localStorage.getItem('siteSettings');
        if (savedSettings) {
          const settings = JSON.parse(savedSettings);
          if (settings && typeof settings === 'object') {
            setSettings(settings);
            return true; // تم تحميل الإعدادات بنجاح
          }
        }
      } catch (error) {
        console.error('Error loading settings from localStorage:', error);
      }
      return false; // لم يتم تحميل الإعدادات
    };

    // تحميل الإعدادات فوراً من localStorage
    const settingsLoaded = loadSettingsFromStorage();

    // جلب الإعدادات الحالية من قاعدة البيانات
    const fetchSettings = async () => {
      try {
        setIsLoading(true);
        const response = await axios.get('/api/settings');

        // استخدام type assertion بطريقة آمنة
        type ResponseType = { settings?: SiteSettings };
        const data = response.data as ResponseType;

        if (data && data.settings) {
          // حفظ الإعدادات في localStorage
          localStorage.setItem('siteSettings', JSON.stringify(data.settings));
          setSettings(data.settings);
        } else if (!settingsLoaded) {
          // إذا لم توجد إعدادات في الخادم ولم تكن محملة من localStorage، استخدم الافتراضية
          setSettings(defaultSettings);
        }
      } catch (error) {
        console.error('Error fetching settings:', error);
        // إذا فشل جلب الإعدادات ولم تكن محملة من localStorage، استخدم الافتراضية
        if (!settingsLoaded) {
          setSettings(defaultSettings);
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchSettings();
  }, []);



  // تطبيق الألوان ديناميكياً
  useEffect(() => {
    // التأكد من أن الكود يعمل في العميل فقط
    if (typeof window === 'undefined') return;

    try {
      // استخدام ألوان الوضع النهاري فقط
      const currentColors = {
        primaryColor: settings.primaryColor || '#169b88',
        secondaryColor: settings.secondaryColor || '#1ab19c',
        sidebarColor: settings.sidebarColor || '#1a202c',
        backgroundColor: settings.backgroundColor || '#f3f4f6',
        accentColor: settings.accentColor || '#10b981',
        textColor: settings.textColor || '#1f2937'
      };

      // تطبيق الألوان فوراً
      applyColors(currentColors);

      // حفظ في localStorage للاستخدام الفوري
      localStorage.setItem('siteColors', JSON.stringify(currentColors));
    } catch (error) {
      console.error('Error applying colors:', error);
    }
  }, [settings]);

  const handleGeneralChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setSettings(prev => ({
      ...prev,
      [name]: value
    }));
  };



  const handleContactChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setSettings(prev => ({
      ...prev,
      contactInfo: {
        ...prev.contactInfo,
        [name]: value
      }
    }));
  };

  const handleDonationInfoChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setSettings(prev => ({
      ...prev,
      contactInfo: {
        ...prev.contactInfo,
        donationInfo: {
          ...prev.contactInfo.donationInfo,
          [name]: value
        }
      }
    }));
  };

  const handleSocialChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setSettings(prev => ({
      ...prev,
      socialLinks: {
        ...prev.socialLinks,
        [name]: value
      }
    }));
  };

  const addHeaderLink = () => {
    if (newHeaderLink.title && newHeaderLink.url) {
      const newId = Date.now().toString();
      const newOrder = settings.headerLinks.length + 1;

      setSettings(prev => ({
        ...prev,
        headerLinks: [
          ...prev.headerLinks,
          {
            id: newId,
            title: newHeaderLink.title,
            url: newHeaderLink.url,
            isActive: true,
            order: newOrder
          }
        ]
      }));

      setNewHeaderLink({ title: '', url: '' });
    } else {
      toast.error('يرجى إدخال العنوان والرابط');
    }
  };

  const removeHeaderLink = (id: string) => {
    setSettings(prev => ({
      ...prev,
      headerLinks: prev.headerLinks.filter(link => link.id !== id)
    }));
  };

  const addFooterLink = () => {
    if (newFooterLink.title && newFooterLink.url) {
      const newId = Date.now().toString();
      const newOrder = settings.footerLinks.length + 1;

      setSettings(prev => ({
        ...prev,
        footerLinks: [
          ...prev.footerLinks,
          {
            id: newId,
            title: newFooterLink.title,
            url: newFooterLink.url,
            isActive: true,
            order: newOrder
          }
        ]
      }));

      setNewFooterLink({ title: '', url: '' });
    } else {
      toast.error('يرجى إدخال العنوان والرابط');
    }
  };

  const removeFooterLink = (id: string) => {
    setSettings(prev => ({
      ...prev,
      footerLinks: prev.footerLinks.filter(link => link.id !== id)
    }));
  };

  const handleToggleHeaderLinkActive = (id: string) => {
    setSettings(prev => ({
      ...prev,
      headerLinks: prev.headerLinks.map(link =>
        link.id === id ? { ...link, isActive: !link.isActive } : link
      )
    }));
  };

  const handleToggleFooterLinkActive = (id: string) => {
    setSettings(prev => ({
      ...prev,
      footerLinks: prev.footerLinks.map(link =>
        link.id === id ? { ...link, isActive: !link.isActive } : link
      )
    }));
  };

  const handleMoveHeaderLink = (id: string, direction: 'up' | 'down') => {
    const links = [...settings.headerLinks];
    const index = links.findIndex(link => link.id === id);

    if (direction === 'up' && index > 0) {
      // Swap with previous item
      [links[index - 1], links[index]] = [links[index], links[index - 1]];
    } else if (direction === 'down' && index < links.length - 1) {
      // Swap with next item
      [links[index], links[index + 1]] = [links[index + 1], links[index]];
    }

    // Update order property
    const updatedLinks = links.map((link, idx) => ({
      ...link,
      order: idx + 1
    }));

    setSettings(prev => ({
      ...prev,
      headerLinks: updatedLinks
    }));
  };

  const handleMoveFooterLink = (id: string, direction: 'up' | 'down') => {
    const links = [...settings.footerLinks];
    const index = links.findIndex(link => link.id === id);

    if (direction === 'up' && index > 0) {
      // Swap with previous item
      [links[index - 1], links[index]] = [links[index], links[index - 1]];
    } else if (direction === 'down' && index < links.length - 1) {
      // Swap with next item
      [links[index], links[index + 1]] = [links[index + 1], links[index]];
    }

    // Update order property
    const updatedLinks = links.map((link, idx) => ({
      ...link,
      order: idx + 1
    }));

    setSettings(prev => ({
      ...prev,
      footerLinks: updatedLinks
    }));
  };

  const handleSaveSettings = async () => {
    try {
      setIsLoading(true);
      const response = await axios.post('/api/settings', { settings });

      // استخدام type assertion بطريقة آمنة
      type ResponseType = { success?: boolean };
      const data = response.data as ResponseType;

      if (data && data.success === true) {
        // حفظ الإعدادات في localStorage للتحميل السريع
        localStorage.setItem('siteSettings', JSON.stringify(settings));
        toast.success('تم حفظ الإعدادات بنجاح');
      } else {
        toast.error('حدث خطأ أثناء حفظ الإعدادات');
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error('فشل في حفظ الإعدادات');
    } finally {
      setIsLoading(false);
    }
  };

  // دالة تطبيق الألوان على الموقع
  const applyColorsToSite = async (colors: SiteColors) => {
    try {
      // تطبيق الألوان فوراً على الصفحة الحالية
      applyColors(colors);

      // استخدام نظام التزامن الجديد لحفظ الألوان في جميع الأماكن
      const { updateColorsEverywhere } = await import('@/utils/colorSync');
      const syncSuccess = await updateColorsEverywhere(colors);

      if (syncSuccess) {
        console.log('✅ تم حفظ ألوان الوضع النهاري بنجاح في جميع الأماكن');
        toast.success('تم حفظ ألوان الوضع النهاري بنجاح');
      } else {
        console.warn('⚠️ تم حفظ الألوان محلياً فقط');
        toast.warn('تم حفظ الألوان محلياً فقط');
      }

    } catch (error) {
      console.error('Error applying colors:', error);
      toast.error('حدث خطأ أثناء تطبيق الألوان');
    }
  };

  return (
    <OptimizedProtectedRoute requiredPermission="admin.settings.view">
      <section className="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8" dir="rtl">
      <div className="max-w-6xl mx-auto">
        <div className="bg-white rounded-lg shadow-md overflow-hidden mb-6">
          <div className="bg-gradient-to-r from-[var(--primary-color)] to-[var(--secondary-color)] px-6 py-4 flex items-center justify-between">
            <h1 className="text-2xl font-bold text-white">إعدادات الموقع</h1>
            <button
              onClick={handleSaveSettings}
              disabled={isLoading}
              className="px-4 py-2 bg-white text-[var(--primary-color)] rounded-md shadow hover:bg-gray-100 transition-colors font-medium flex items-center"
            >
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-[var(--primary-color)]" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  جاري الحفظ...
                </>
              ) : (
                <>
                  <FaCog className="ml-2" />
                  حفظ الإعدادات
                </>
              )}
            </button>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="p-4 sm:p-6">
            <TabsList className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-7 mb-6 h-auto gap-1">
              <TabsTrigger value="general" className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 p-2 text-xs sm:text-sm">
                <FaGlobe className="text-sm sm:text-base" />
                <span className="hidden sm:inline">عام</span>
                <span className="sm:hidden">عام</span>
              </TabsTrigger>
              <TabsTrigger value="icons" className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 p-2 text-xs sm:text-sm">
                <FaImage className="text-sm sm:text-base" />
                <span className="hidden lg:inline">أيقونات الهيدر</span>
                <span className="lg:hidden">أيقونات</span>
              </TabsTrigger>
              <TabsTrigger value="header" className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 p-2 text-xs sm:text-sm">
                <FaList className="text-sm sm:text-base" />
                <span className="hidden sm:inline">الهيدر</span>
                <span className="sm:hidden">هيدر</span>
              </TabsTrigger>
              <TabsTrigger value="footer" className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 p-2 text-xs sm:text-sm">
                <FaList className="text-sm sm:text-base" />
                <span className="hidden sm:inline">الفوتر</span>
                <span className="sm:hidden">فوتر</span>
              </TabsTrigger>
              <TabsTrigger value="social" className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 p-2 text-xs sm:text-sm">
                <FaFacebook className="text-sm sm:text-base" />
                <span className="hidden lg:inline">التواصل الاجتماعي</span>
                <span className="lg:hidden">تواصل</span>
              </TabsTrigger>
              <TabsTrigger value="contact" className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 p-2 text-xs sm:text-sm">
                <FaPhone className="text-sm sm:text-base" />
                <span className="hidden lg:inline">معلومات الاتصال</span>
                <span className="lg:hidden">اتصال</span>
              </TabsTrigger>
              <TabsTrigger value="page-backgrounds" className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 p-2 text-xs sm:text-sm">
                <FaImage className="text-sm sm:text-base" />
                <span className="hidden lg:inline">خلفيات الصفحات العامة</span>
                <span className="lg:hidden">خلفيات</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="general" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FaGlobe className="text-[var(--primary-color)]" />
                    <span>الإعدادات العامة</span>
                  </CardTitle>
                  <CardDescription>
                    إعدادات الموقع الأساسية مثل الاسم والوصف والألوان
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label htmlFor="siteName" className="block text-sm font-medium text-gray-700 mb-1">اسم الموقع</label>
                    <input
                      id="siteName"
                      name="siteName"
                      type="text"
                      value={settings.siteName}
                      onChange={handleGeneralChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
                    />
                  </div>

                  <div>
                    <label htmlFor="siteDescription" className="block text-sm font-medium text-gray-700 mb-1">وصف الموقع</label>
                    <textarea
                      id="siteDescription"
                      name="siteDescription"
                      value={settings.siteDescription}
                      onChange={handleGeneralChange}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
                    />
                  </div>





                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="primaryColor" className="block text-sm font-medium text-gray-700 mb-1">اللون الرئيسي</label>
                        <div className="flex">
                          <input
                            id="primaryColor"
                            name="primaryColor"
                            type="text"
                            value={settings.primaryColor}
                            onChange={handleGeneralChange}
                            className="flex-1 px-3 py-2 border border-gray-300 rounded-r-none rounded-l-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
                          />
                          <div className="w-10 border border-r-gray-300 border-l-0 border-y-gray-300 rounded-l-none rounded-r-md flex items-center justify-center">
                            <input
                              type="color"
                              value={settings.primaryColor}
                              onChange={(e) => {
                                setSettings(prev => ({
                                  ...prev,
                                  primaryColor: e.target.value
                                }));
                              }}
                              className="w-6 h-6 cursor-pointer"
                            />
                          </div>
                        </div>
                      </div>

                      <div>
                        <label htmlFor="secondaryColor" className="block text-sm font-medium text-gray-700 mb-1">اللون الثانوي</label>
                        <div className="flex">
                          <input
                            id="secondaryColor"
                            name="secondaryColor"
                            type="text"
                            value={settings.secondaryColor}
                            onChange={handleGeneralChange}
                            className="flex-1 px-3 py-2 border border-gray-300 rounded-r-none rounded-l-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
                          />
                          <div className="w-10 border border-r-gray-300 border-l-0 border-y-gray-300 rounded-l-none rounded-r-md flex items-center justify-center">
                            <input
                              type="color"
                              value={settings.secondaryColor}
                              onChange={(e) => {
                                setSettings(prev => ({
                                  ...prev,
                                  secondaryColor: e.target.value
                                }));
                              }}
                              className="w-6 h-6 cursor-pointer"
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="sidebarColor" className="block text-sm font-medium text-gray-700 mb-1">لون الشريط الجانبي</label>
                        <div className="flex">
                          <input
                            id="sidebarColor"
                            name="sidebarColor"
                            type="text"
                            value={settings.sidebarColor}
                            onChange={handleGeneralChange}
                            className="flex-1 px-3 py-2 border border-gray-300 rounded-r-none rounded-l-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
                          />
                          <div className="w-10 border border-r-gray-300 border-l-0 border-y-gray-300 rounded-l-none rounded-r-md flex items-center justify-center">
                            <input
                              type="color"
                              value={settings.sidebarColor}
                              onChange={(e) => {
                                setSettings(prev => ({
                                  ...prev,
                                  sidebarColor: e.target.value
                                }));
                              }}
                              className="w-6 h-6 cursor-pointer"
                            />
                          </div>
                        </div>
                      </div>

                      <div>
                        <label htmlFor="backgroundColor" className="block text-sm font-medium text-gray-700 mb-1">لون خلفية الموقع</label>
                        <div className="flex">
                          <input
                            id="backgroundColor"
                            name="backgroundColor"
                            type="text"
                            value={settings.backgroundColor}
                            onChange={handleGeneralChange}
                            className="flex-1 px-3 py-2 border border-gray-300 rounded-r-none rounded-l-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
                          />
                          <div className="w-10 border border-r-gray-300 border-l-0 border-y-gray-300 rounded-l-none rounded-r-md flex items-center justify-center">
                            <input
                              type="color"
                              value={settings.backgroundColor}
                              onChange={(e) => {
                                setSettings(prev => ({
                                  ...prev,
                                  backgroundColor: e.target.value
                                }));
                              }}
                              className="w-6 h-6 cursor-pointer"
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="accentColor" className="block text-sm font-medium text-gray-700 mb-1">اللون المميز</label>
                        <div className="flex">
                          <input
                            id="accentColor"
                            name="accentColor"
                            type="text"
                            value={settings.accentColor}
                            onChange={handleGeneralChange}
                            className="flex-1 px-3 py-2 border border-gray-300 rounded-r-none rounded-l-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
                          />
                          <div className="w-10 border border-r-gray-300 border-l-0 border-y-gray-300 rounded-l-none rounded-r-md flex items-center justify-center">
                            <input
                              type="color"
                              value={settings.accentColor}
                              onChange={(e) => {
                                setSettings(prev => ({
                                  ...prev,
                                  accentColor: e.target.value
                                }));
                              }}
                              className="w-6 h-6 cursor-pointer"
                            />
                          </div>
                        </div>
                      </div>

                      <div>
                        <label htmlFor="textColor" className="block text-sm font-medium text-gray-700 mb-1">لون النص الرئيسي</label>
                        <div className="flex">
                          <input
                            id="textColor"
                            name="textColor"
                            type="text"
                            value={settings.textColor}
                            onChange={handleGeneralChange}
                            className="flex-1 px-3 py-2 border border-gray-300 rounded-r-none rounded-l-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
                          />
                          <div className="w-10 border border-r-gray-300 border-l-0 border-y-gray-300 rounded-l-none rounded-r-md flex items-center justify-center">
                            <input
                              type="color"
                              value={settings.textColor}
                              onChange={(e) => {
                                setSettings(prev => ({
                                  ...prev,
                                  textColor: e.target.value
                                }));
                              }}
                              className="w-6 h-6 cursor-pointer"
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* زر تطبيق الألوان */}
                    <div className="mt-6 p-4 bg-blue-50 rounded-md border border-blue-200">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-lg font-medium text-blue-900 mb-2">تطبيق الألوان على الموقع</h3>
                          <p className="text-sm text-blue-700">اضغط هنا لتطبيق الألوان الجديدة على جميع صفحات الموقع</p>
                        </div>
                        <button
                          onClick={async () => {
                            // التأكد من أن الكود يعمل في العميل فقط
                            if (typeof window === 'undefined') return;

                            // تطبيق الألوان فوراً
                            const colors = {
                              primaryColor: settings.primaryColor,
                              secondaryColor: settings.secondaryColor,
                              sidebarColor: settings.sidebarColor,
                              backgroundColor: settings.backgroundColor,
                              accentColor: settings.accentColor,
                              textColor: settings.textColor
                            };

                            await applyColorsToSite(colors);
                            toast.success('تم تطبيق الألوان بنجاح على جميع صفحات الموقع!');
                          }}
                          className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                        >
                          تطبيق الألوان على الموقع كاملاً
                        </button>
                      </div>
                    </div>

                    {/* معلومات التباين */}
                    <div className="mt-4 p-4 bg-yellow-50 rounded-md border border-yellow-200">
                      <div className="flex items-start gap-3">
                        <div className="text-yellow-600 text-xl">💡</div>
                        <div>
                          <h4 className="text-lg font-medium text-yellow-900 mb-2">نصائح للتباين والوضوح</h4>
                          <ul className="text-sm text-yellow-800 space-y-1">
                            <li>• يتم حساب لون النص تلقائياً ليكون واضحاً على الخلفية</li>
                            <li>• الألوان الفاتحة ستحصل على نص داكن</li>
                            <li>• الألوان الداكنة ستحصل على نص أبيض</li>
                            <li>• استخدم زر مختبر التباين 🎨 لاختبار الألوان</li>
                            <li>• تأكد من وضوح النصوص على جميع الخلفيات</li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    {/* أزرار التحكم في ستايل الموقع */}
                    <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-md border border-blue-200">
                      <h3 className="text-lg font-medium text-blue-900 mb-4">🎨 تغيير ستايل الموقع</h3>

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        {/* أخضر */}
                        <button
                          onClick={() => {
                            const newColors = {
                              primaryColor: '#169b88',
                              secondaryColor: '#1ab19c',
                              sidebarColor: '#1a202c',
                              backgroundColor: '#f3f4f6',
                              accentColor: '#10b981',
                              textColor: '#1f2937'
                            };
                            setSettings(prev => ({ ...prev, ...newColors }));
                            applyColorsToSite(newColors);
                            toast.success('🟢 أخضر');
                          }}
                          className="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-4 rounded-lg transition-all transform hover:scale-105 shadow-lg"
                        >
                          🟢 أخضر
                        </button>

                        {/* أزرق */}
                        <button
                          onClick={() => {
                            const newColors = {
                              primaryColor: '#3b82f6',
                              secondaryColor: '#60a5fa',
                              sidebarColor: '#3b82f6',
                              backgroundColor: '#f3f4f6',
                              accentColor: '#2563eb',
                              textColor: '#1f2937'
                            };
                            setSettings(prev => ({ ...prev, ...newColors }));
                            applyColorsToSite(newColors);
                            toast.success('🔵 أزرق');
                          }}
                          className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-4 rounded-lg transition-all transform hover:scale-105 shadow-lg"
                        >
                          🔵 أزرق
                        </button>

                        {/* بنفسجي */}
                        <button
                          onClick={() => {
                            const newColors = {
                              primaryColor: '#8b5cf6',
                              secondaryColor: '#a78bfa',
                              sidebarColor: '#8b5cf6',
                              backgroundColor: '#f3f4f6',
                              accentColor: '#7c3aed',
                              textColor: '#1f2937'
                            };
                            setSettings(prev => ({ ...prev, ...newColors }));
                            applyColorsToSite(newColors);
                            toast.success('🟣 بنفسجي');
                          }}
                          className="bg-violet-500 hover:bg-violet-600 text-white font-bold py-3 px-4 rounded-lg transition-all transform hover:scale-105 shadow-lg"
                        >
                          🟣 بنفسجي
                        </button>

                        {/* وردي */}
                        <button
                          onClick={() => {
                            const newColors = {
                              primaryColor: '#ec4899',
                              secondaryColor: '#f472b6',
                              sidebarColor: '#ec4899',
                              backgroundColor: '#f3f4f6',
                              accentColor: '#db2777',
                              textColor: '#1f2937'
                            };
                            setSettings(prev => ({ ...prev, ...newColors }));
                            applyColorsToSite(newColors);
                            toast.success('🩷 وردي');
                          }}
                          className="bg-pink-500 hover:bg-pink-600 text-white font-bold py-3 px-4 rounded-lg transition-all transform hover:scale-105 shadow-lg"
                        >
                          🩷 وردي
                        </button>

                        {/* برتقالي */}
                        <button
                          onClick={() => {
                            const newColors = {
                              primaryColor: '#f97316',
                              secondaryColor: '#fb923c',
                              sidebarColor: '#f97316',
                              backgroundColor: '#f3f4f6',
                              accentColor: '#ea580c',
                              textColor: '#1f2937'
                            };
                            setSettings(prev => ({ ...prev, ...newColors }));
                            applyColorsToSite(newColors);
                            toast.success('🟠 برتقالي');
                          }}
                          className="bg-orange-500 hover:bg-orange-600 text-white font-bold py-3 px-4 rounded-lg transition-all transform hover:scale-105 shadow-lg"
                        >
                          🟠 برتقالي
                        </button>

                        {/* أحمر */}
                        <button
                          onClick={() => {
                            const newColors = {
                              primaryColor: '#ef4444',
                              secondaryColor: '#f87171',
                              sidebarColor: '#ef4444',
                              backgroundColor: '#f3f4f6',
                              accentColor: '#dc2626',
                              textColor: '#1f2937'
                            };
                            setSettings(prev => ({ ...prev, ...newColors }));
                            applyColorsToSite(newColors);
                            toast.success('🔴 أحمر');
                          }}
                          className="bg-red-500 hover:bg-red-600 text-white font-bold py-3 px-4 rounded-lg transition-all transform hover:scale-105 shadow-lg"
                        >
                          🔴 أحمر
                        </button>

                        {/* أصفر */}
                        <button
                          onClick={() => {
                            const newColors = {
                              primaryColor: '#eab308',
                              secondaryColor: '#facc15',
                              sidebarColor: '#eab308',
                              backgroundColor: '#f3f4f6',
                              accentColor: '#ca8a04',
                              textColor: '#1f2937'
                            };
                            setSettings(prev => ({ ...prev, ...newColors }));
                            applyColorsToSite(newColors);
                            toast.success('🟡 أصفر');
                          }}
                          className="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-3 px-4 rounded-lg transition-all transform hover:scale-105 shadow-lg"
                        >
                          🟡 أصفر
                        </button>

                        {/* رمادي */}
                        <button
                          onClick={() => {
                            const newColors = {
                              primaryColor: '#374151',
                              secondaryColor: '#4b5563',
                              sidebarColor: '#374151',
                              backgroundColor: '#f3f4f6',
                              accentColor: '#1f2937',
                              textColor: '#1f2937'
                            };
                            setSettings(prev => ({ ...prev, ...newColors }));
                            applyColorsToSite(newColors);
                            toast.success('⚫ رمادي');
                          }}
                          className="bg-gray-600 hover:bg-gray-700 text-white font-bold py-3 px-4 rounded-lg transition-all transform hover:scale-105 shadow-lg"
                        >
                          ⚫ رمادي
                        </button>
                      </div>

                      <div className="mt-4 flex flex-col items-center gap-3">
                        <button
                          onClick={async () => {
                            // حفظ الإعدادات مباشرة
                            await handleSaveSettings();
                            toast.success('تم حفظ الستايل الجديد بنجاح! سيتم تطبيقه على جميع صفحات الموقع.');
                          }}
                          className="bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-bold py-2 px-6 rounded-full transition-all transform hover:scale-105 shadow-lg"
                        >
                          💾 حفظ الستايل الجديد
                        </button>
                        <p className="text-sm text-gray-600 text-center">
                          ملاحظة: سيتم تطبيق الألوان الجديدة على جميع صفحات الموقع تلقائياً
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="mt-6 p-4 bg-gray-50 rounded-md border border-gray-200">
                    <h3 className="text-lg font-medium text-gray-900 mb-3">إعدادات التسجيل</h3>
                    <div className="flex items-center">
                      <input
                        id="registrationEnabled"
                        name="registrationEnabled"
                        type="checkbox"
                        checked={settings.registrationEnabled}
                        onChange={(e) => {
                          setSettings(prev => ({
                            ...prev,
                            registrationEnabled: e.target.checked
                          }));
                        }}
                        className="h-5 w-5 text-[var(--primary-color)] focus:ring-[var(--primary-color)] border-gray-300 rounded ml-3"
                      />
                      <div>
                        <label htmlFor="registrationEnabled" className="font-medium text-gray-700">تفعيل التسجيل في الموقع</label>
                        <p className="text-sm text-gray-500 mt-1">عند تعطيل هذا الخيار، لن يتمكن المستخدمون الجدد من إنشاء حسابات في الموقع</p>
                      </div>
                    </div>
                  </div>


                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="icons" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FaImage className="text-[var(--primary-color)]" />
                    <span>أيقونات الهيدر</span>
                  </CardTitle>
                  <CardDescription>
                    رفع وإدارة أيقونات الهيدر (اللوجو والفافيكون)
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4 sm:space-y-6">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                    {/* رفع اللوجو */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium text-gray-900">شعار الموقع (اللوجو)</h3>
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 sm:p-6 text-center">
                        {settings.logoUrl && (
                          <div className="mb-4">
                            <div className="relative mx-auto h-20 w-20">
                              <Image
                                src={settings.logoUrl}
                                alt="اللوجو الحالي"
                                fill
                                className="object-contain"
                                sizes="80px"
                                onError={(e) => {
                                  console.error('فشل تحميل الشعار:', settings.logoUrl);
                                  // محاولة تحميل من مسار بديل
                                  const img = e.target as HTMLImageElement;
                                  if (!img.src.includes('/api/uploads/')) {
                                    img.src = settings.logoUrl.replace('/uploads/', '/api/uploads/');
                                  }
                                }}
                              />
                            </div>
                            <p className="text-sm text-gray-500 mt-2">اللوجو الحالي</p>
                            <div className="flex justify-center gap-2 mt-3">
                              <button
                                type="button"
                                onClick={() => {
                                  if (window.confirm('هل أنت متأكد من حذف الشعار؟')) {
                                    const updatedSettings = {
                                      ...settings,
                                      logoUrl: '/logo.svg' // العودة للشعار الافتراضي
                                    };
                                    setSettings(updatedSettings);
                                    // حفظ التغييرات
                                    axios.post('/api/settings', { settings: updatedSettings })
                                      .then(() => {
                                        localStorage.setItem('siteSettings', JSON.stringify(updatedSettings));
                                        window.dispatchEvent(new CustomEvent('siteSettingsUpdated', { detail: updatedSettings }));
                                        toast.success('تم حذف الشعار بنجاح');
                                      })
                                      .catch(() => {
                                        toast.error('فشل في حذف الشعار');
                                      });
                                  }
                                }}
                                className="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
                              >
                                🗑️ حذف
                              </button>
                              <label
                                htmlFor="logoReplace"
                                className="cursor-pointer px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                              >
                                ✏️ تعديل
                              </label>
                              <input
                                type="file"
                                id="logoReplace"
                                accept="image/*"
                                className="hidden"
                                onChange={async (e) => {
                                  const file = e.target.files?.[0];
                                  if (file) {
                                    setUploadingLogo(true);
                                    const formData = new FormData();
                                    formData.append('file', file);
                                    formData.append('type', 'header-icons');
                                    try {
                                      const response = await axios.post('/api/upload', formData);
                                      if ((response.data as { data: { filePath: string } }).data?.filePath) {
                                        const newLogoUrl = (response.data as { data: { filePath: string } }).data.filePath;
                                        const updatedSettings = {
                                          ...settings,
                                          logoUrl: newLogoUrl
                                        };
                                        setSettings(updatedSettings);
                                        await axios.post('/api/settings', { settings: updatedSettings });
                                        localStorage.setItem('siteSettings', JSON.stringify(updatedSettings));
                                        window.dispatchEvent(new CustomEvent('siteSettingsUpdated', { detail: updatedSettings }));
                                        toast.success('تم تعديل الشعار بنجاح');
                                      }
                                    } catch (error) {
                                      console.error('Error uploading logo:', error);
                                      toast.error('فشل في تعديل الشعار');
                                    } finally {
                                      setUploadingLogo(false);
                                    }
                                  }
                                }}
                              />
                            </div>
                          </div>
                        )}
                        <input
                          type="file"
                          id="logoUpload"
                          accept="image/*"
                          className="hidden"
                          onChange={async (e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              setUploadingLogo(true);
                              const formData = new FormData();
                              formData.append('file', file);
                              formData.append('type', 'header-icons');
                              try {
                                const response = await axios.post('/api/upload', formData);
                                if ((response.data as { data: { filePath: string } }).data?.filePath) {
                                  const newLogoUrl = (response.data as { data: { filePath: string } }).data.filePath;
                                  const updatedSettings = {
                                    ...settings,
                                    logoUrl: newLogoUrl
                                  };
                                  setSettings(updatedSettings);

                                  // حفظ الإعدادات تلقائياً في قاعدة البيانات
                                  try {
                                    await axios.post('/api/settings', { settings: updatedSettings });
                                    // حفظ في localStorage أيضاً للتحميل السريع
                                    localStorage.setItem('siteSettings', JSON.stringify(updatedSettings));
                                    // إرسال حدث للمكونات الأخرى للتحديث
                                    window.dispatchEvent(new CustomEvent('siteSettingsUpdated', { detail: updatedSettings }));
                                    toast.success('تم رفع وحفظ الشعار بنجاح');
                                  } catch (saveError) {
                                    console.error('Error saving settings:', saveError);
                                    toast.warning('تم رفع الشعار ولكن فشل في الحفظ التلقائي');
                                  }
                                }
                              } catch (error) {
                                console.error('Error uploading logo:', error);
                                toast.error('فشل في رفع الشعار');
                              } finally {
                                setUploadingLogo(false);
                              }
                            }
                          }}
                        />
                        <label
                          htmlFor="logoUpload"
                          className="cursor-pointer inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] transition-colors"
                        >
                          {uploadingLogo ? (
                            <>
                              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              جاري الرفع...
                            </>
                          ) : (
                            <>
                              <FaImage className="ml-2" />
                              {settings.logoUrl && settings.logoUrl !== '/logo.svg' ? 'رفع شعار جديد' : 'رفع شعار'}
                            </>
                          )}
                        </label>
                        <p className="text-xs text-gray-500 mt-2">
                          الحد الأقصى: 5 ميجابايت | الأنواع المدعومة: JPG, PNG, GIF, WebP, SVG
                        </p>
                      </div>
                    </div>

                    {/* رفع الفافيكون */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium text-gray-900">أيقونة الموقع (Favicon)</h3>
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                        {settings.faviconUrl && (
                          <div className="mb-4">
                            <div className="relative mx-auto h-8 w-8">
                              <Image
                                src={settings.faviconUrl}
                                alt="الفافيكون الحالي"
                                fill
                                className="object-contain"
                                sizes="32px"
                                onError={(e) => {
                                  console.error('فشل تحميل الفافيكون:', settings.faviconUrl);
                                  // محاولة تحميل من مسار بديل
                                  const img = e.target as HTMLImageElement;
                                  if (!img.src.includes('/api/uploads/')) {
                                    img.src = settings.faviconUrl.replace('/uploads/', '/api/uploads/');
                                  }
                                }}
                              />
                            </div>
                            <p className="text-sm text-gray-500 mt-2">الفافيكون الحالي</p>
                            <div className="flex justify-center gap-2 mt-3">
                              <button
                                type="button"
                                onClick={() => {
                                  if (window.confirm('هل أنت متأكد من حذف الفافيكون؟')) {
                                    const updatedSettings = {
                                      ...settings,
                                      faviconUrl: '/favicon.ico' // العودة للفافيكون الافتراضي
                                    };
                                    setSettings(updatedSettings);
                                    updateFaviconImmediately('/favicon.ico');
                                    // حفظ التغييرات
                                    axios.post('/api/settings', { settings: updatedSettings })
                                      .then(() => {
                                        localStorage.setItem('siteSettings', JSON.stringify(updatedSettings));
                                        window.dispatchEvent(new CustomEvent('siteSettingsUpdated', { detail: updatedSettings }));
                                        toast.success('تم حذف الفافيكون بنجاح');
                                      })
                                      .catch(() => {
                                        toast.error('فشل في حذف الفافيكون');
                                      });
                                  }
                                }}
                                className="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
                              >
                                🗑️ حذف
                              </button>
                              <label
                                htmlFor="faviconReplace"
                                className="cursor-pointer px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                              >
                                ✏️ تعديل
                              </label>
                              <input
                                type="file"
                                id="faviconReplace"
                                accept="image/*"
                                className="hidden"
                                onChange={async (e) => {
                                  const file = e.target.files?.[0];
                                  if (file) {
                                    setUploadingFavicon(true);
                                    const formData = new FormData();
                                    formData.append('file', file);
                                    formData.append('type', 'header-icons');
                                    try {
                                      const response = await axios.post('/api/upload', formData);
                                      if ((response.data as { data: { filePath: string } }).data?.filePath) {
                                        const newFaviconUrl = (response.data as { data: { filePath: string } }).data.filePath;
                                        const updatedSettings = {
                                          ...settings,
                                          faviconUrl: newFaviconUrl
                                        };
                                        setSettings(updatedSettings);
                                        updateFaviconImmediately(newFaviconUrl);
                                        await axios.post('/api/settings', { settings: updatedSettings });
                                        localStorage.setItem('siteSettings', JSON.stringify(updatedSettings));
                                        window.dispatchEvent(new CustomEvent('siteSettingsUpdated', { detail: updatedSettings }));
                                        toast.success('تم تعديل الفافيكون بنجاح');
                                      }
                                    } catch (error) {
                                      console.error('Error uploading favicon:', error);
                                      toast.error('فشل في تعديل الفافيكون');
                                    } finally {
                                      setUploadingFavicon(false);
                                    }
                                  }
                                }}
                              />
                            </div>
                          </div>
                        )}
                        <input
                          type="file"
                          id="faviconUpload"
                          accept="image/*"
                          className="hidden"
                          onChange={async (e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              setUploadingFavicon(true);
                              const formData = new FormData();
                              formData.append('file', file);
                              formData.append('type', 'header-icons');
                              try {
                                const response = await axios.post('/api/upload', formData);
                                if ((response.data as { data: { filePath: string } }).data?.filePath) {
                                  const newFaviconUrl = (response.data as { data: { filePath: string } }).data.filePath;
                                  const updatedSettings = {
                                    ...settings,
                                    faviconUrl: newFaviconUrl
                                  };
                                  setSettings(updatedSettings);

                                  // تحديث الفافيكون فوراً
                                  updateFaviconImmediately(newFaviconUrl);

                                  // حفظ الإعدادات تلقائياً في قاعدة البيانات
                                  try {
                                    await axios.post('/api/settings', { settings: updatedSettings });
                                    // حفظ في localStorage أيضاً للتحميل السريع
                                    localStorage.setItem('siteSettings', JSON.stringify(updatedSettings));
                                    // إرسال حدث للمكونات الأخرى للتحديث
                                    window.dispatchEvent(new CustomEvent('siteSettingsUpdated', { detail: updatedSettings }));
                                    toast.success('تم رفع وحفظ الأيقونة بنجاح');
                                  } catch (saveError) {
                                    console.error('Error saving settings:', saveError);
                                    toast.warning('تم رفع الأيقونة ولكن فشل في الحفظ التلقائي');
                                  }
                                }
                              } catch (error) {
                                console.error('Error uploading favicon:', error);
                                toast.error('فشل في رفع الأيقونة');
                              } finally {
                                setUploadingFavicon(false);
                              }
                            }
                          }}
                        />
                        <label
                          htmlFor="faviconUpload"
                          className="cursor-pointer inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] transition-colors"
                        >
                          {uploadingFavicon ? (
                            <>
                              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              جاري الرفع...
                            </>
                          ) : (
                            <>
                              <FaImage className="ml-2" />
                              {settings.faviconUrl && settings.faviconUrl !== '/favicon.ico' ? 'رفع فافيكون جديد' : 'رفع فافيكون'}
                            </>
                          )}
                        </label>
                        <p className="text-xs text-gray-500 mt-2">
                          الحد الأقصى: 5 ميجابايت | الأنواع المدعومة: JPG, PNG, GIF, WebP, SVG
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="header" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FaList className="text-[var(--primary-color)]" />
                    <span>روابط الهيدر</span>
                  </CardTitle>
                  <CardDescription>
                    إدارة الروابط التي تظهر في شريط التنقل العلوي (الهيدر)
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex flex-col space-y-4">
                    {(settings?.headerLinks || []).map((link) => (
                      <div key={link.id} className="flex items-center space-x-4 space-x-reverse p-3 bg-gray-50 rounded-md">
                        <div className="flex-1">
                          <div className="font-medium">{link.title}</div>
                          <div className="text-sm text-gray-500">{link.url}</div>
                          {link.iconUrl && (
                            <div className="text-xs text-blue-600 mt-1 flex items-center gap-2">
                              <div className="relative w-4 h-4">
                                <Image src={link.iconUrl} alt="أيقونة" fill className="object-contain" sizes="16px" />
                              </div>
                              <span>أيقونة مرفوعة</span>
                            </div>
                          )}
                        </div>
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <label htmlFor={`icon-${link.id}`} className="p-1 text-blue-500 hover:text-blue-700 cursor-pointer" title="رفع أيقونة">
                            <FaImage />
                          </label>
                          <input
                            type="file"
                            id={`icon-${link.id}`}
                            accept="image/*"
                            className="hidden"
                            onChange={async (e) => {
                              const file = e.target.files?.[0];
                              if (file) {
                                const formData = new FormData();
                                formData.append('file', file);
                                formData.append('type', 'header-icons');
                                try {
                                  const response = await axios.post('/api/upload', formData);
                                  if ((response.data as { data: { filePath: string } }).data?.filePath) {
                                    setSettings(prev => ({
                                      ...prev,
                                      headerLinks: prev.headerLinks.map(l =>
                                        l.id === link.id
                                          ? { ...l, iconUrl: (response.data as { data: { filePath: string } }).data.filePath }
                                          : l
                                      )
                                    }));
                                    toast.success('تم رفع الأيقونة بنجاح');
                                  }
                                } catch (error) {
                                  console.error('Error uploading icon:', error);
                                  toast.error('فشل في رفع الأيقونة');
                                }
                              }
                            }}
                          />
                          {link.iconUrl && (
                            <button
                              type="button"
                              onClick={() => {
                                setSettings(prev => ({
                                  ...prev,
                                  headerLinks: prev.headerLinks.map(l =>
                                    l.id === link.id
                                      ? { ...l, iconUrl: undefined }
                                      : l
                                  )
                                }));
                              }}
                              className="p-1 text-orange-500 hover:text-orange-700"
                              title="حذف الأيقونة"
                            >
                              🗑️
                            </button>
                          )}
                          <button
                            type="button"
                            onClick={() => handleMoveHeaderLink(link.id, 'up')}
                            className="p-1 text-gray-500 hover:text-[var(--primary-color)]"
                            title="تحريك لأعلى"
                          >
                            ↑
                          </button>
                          <button
                            type="button"
                            onClick={() => handleMoveHeaderLink(link.id, 'down')}
                            className="p-1 text-gray-500 hover:text-[var(--primary-color)]"
                            title="تحريك لأسفل"
                          >
                            ↓
                          </button>
                          <button
                            type="button"
                            onClick={() => handleToggleHeaderLinkActive(link.id)}
                            className={`p-1 ${link.isActive ? 'text-primary-color' : 'text-gray-400'}`}
                            title={link.isActive ? 'تعطيل' : 'تفعيل'}
                          >
                            {link.isActive ? '✓' : '✗'}
                          </button>
                          <button
                            type="button"
                            onClick={() => removeHeaderLink(link.id)}
                            className="p-1 text-red-500 hover:text-red-700"
                            title="حذف"
                          >
                            ×
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="mt-4 p-4 border border-dashed border-gray-300 rounded-md">
                    <h4 className="text-sm font-medium mb-2">إضافة رابط جديد</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="newHeaderLinkTitle" className="block text-xs text-gray-500 mb-1">العنوان</label>
                        <input
                          id="newHeaderLinkTitle"
                          type="text"
                          value={newHeaderLink.title}
                          onChange={(e) => setNewHeaderLink({ ...newHeaderLink, title: e.target.value })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
                          placeholder="مثال: من نحن"
                        />
                      </div>
                      <div>
                        <label htmlFor="newHeaderLinkUrl" className="block text-xs text-gray-500 mb-1">الرابط</label>
                        <input
                          id="newHeaderLinkUrl"
                          type="text"
                          value={newHeaderLink.url}
                          onChange={(e) => setNewHeaderLink({ ...newHeaderLink, url: e.target.value })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
                          placeholder="مثال: /about"
                        />
                      </div>
                    </div>
                    <button
                      type="button"
                      onClick={addHeaderLink}
                      className="mt-3 w-full bg-[var(--primary-color)] text-white py-2 rounded-md hover:bg-[var(--secondary-color)] transition-colors"
                    >
                      إضافة رابط
                    </button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="footer" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FaList className="text-[var(--primary-color)]" />
                    <span>روابط الفوتر</span>
                  </CardTitle>
                  <CardDescription>
                    إدارة الروابط التي تظهر في تذييل الصفحة (الفوتر)
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex flex-col space-y-4">
                    {(settings?.footerLinks || []).map((link) => (
                      <div key={link.id} className="flex items-center space-x-4 space-x-reverse p-3 bg-gray-50 rounded-md">
                        <div className="flex-1">
                          <div className="font-medium">{link.title}</div>
                          <div className="text-sm text-gray-500">{link.url}</div>
                        </div>
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <button
                            type="button"
                            onClick={() => handleMoveFooterLink(link.id, 'up')}
                            className="p-1 text-gray-500 hover:text-[var(--primary-color)]"
                            title="تحريك لأعلى"
                          >
                            ↑
                          </button>
                          <button
                            type="button"
                            onClick={() => handleMoveFooterLink(link.id, 'down')}
                            className="p-1 text-gray-500 hover:text-[var(--primary-color)]"
                            title="تحريك لأسفل"
                          >
                            ↓
                          </button>
                          <button
                            type="button"
                            onClick={() => handleToggleFooterLinkActive(link.id)}
                            className={`p-1 ${link.isActive ? 'text-primary-color' : 'text-gray-400'}`}
                            title={link.isActive ? 'تعطيل' : 'تفعيل'}
                          >
                            {link.isActive ? '✓' : '✗'}
                          </button>
                          <button
                            type="button"
                            onClick={() => removeFooterLink(link.id)}
                            className="p-1 text-red-500 hover:text-red-700"
                            title="حذف"
                          >
                            ×
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="mt-4 p-4 border border-dashed border-gray-300 rounded-md">
                    <h4 className="text-sm font-medium mb-2">إضافة رابط جديد</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="newFooterLinkTitle" className="block text-xs text-gray-500 mb-1">العنوان</label>
                        <input
                          id="newFooterLinkTitle"
                          type="text"
                          value={newFooterLink.title}
                          onChange={(e) => setNewFooterLink({ ...newFooterLink, title: e.target.value })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
                          placeholder="مثال: سياسة الخصوصية"
                        />
                      </div>
                      <div>
                        <label htmlFor="newFooterLinkUrl" className="block text-xs text-gray-500 mb-1">الرابط</label>
                        <input
                          id="newFooterLinkUrl"
                          type="text"
                          value={newFooterLink.url}
                          onChange={(e) => setNewFooterLink({ ...newFooterLink, url: e.target.value })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
                          placeholder="مثال: /privacy"
                        />
                      </div>
                    </div>
                    <button
                      type="button"
                      onClick={addFooterLink}
                      className="mt-3 w-full bg-[var(--primary-color)] text-white py-2 rounded-md hover:bg-[var(--secondary-color)] transition-colors"
                    >
                      إضافة رابط
                    </button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="social" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FaFacebook className="text-[var(--primary-color)]" />
                    <span>وسائل التواصل الاجتماعي</span>
                  </CardTitle>
                  <CardDescription>
                    إدارة روابط وسائل التواصل الاجتماعي التي تظهر في الموقع
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label htmlFor="facebook" className="block text-sm font-medium text-gray-700 mb-1">فيسبوك</label>
                    <div className="flex">
                      <input
                        id="facebook"
                        name="facebook"
                        type="text"
                        value={settings.socialLinks.facebook}
                        onChange={handleSocialChange}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-r-none rounded-l-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
                        placeholder="https://facebook.com/yourpage"
                      />
                      <div className="px-3 py-2 bg-gray-100 border border-r-gray-300 border-l-0 border-y-gray-300 rounded-l-none rounded-r-md flex items-center">
                        <FaFacebook className="text-blue-600" />
                      </div>
                    </div>
                  </div>

                  <div>
                    <label htmlFor="twitter" className="block text-sm font-medium text-gray-700 mb-1">تويتر</label>
                    <div className="flex">
                      <input
                        id="twitter"
                        name="twitter"
                        type="text"
                        value={settings.socialLinks.twitter}
                        onChange={handleSocialChange}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-r-none rounded-l-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
                        placeholder="https://twitter.com/yourhandle"
                      />
                      <div className="px-3 py-2 bg-gray-100 border border-r-gray-300 border-l-0 border-y-gray-300 rounded-l-none rounded-r-md flex items-center">
                        <FaTwitter className="text-blue-400" />
                      </div>
                    </div>
                  </div>

                  <div>
                    <label htmlFor="instagram" className="block text-sm font-medium text-gray-700 mb-1">انستغرام</label>
                    <div className="flex">
                      <input
                        id="instagram"
                        name="instagram"
                        type="text"
                        value={settings.socialLinks.instagram}
                        onChange={handleSocialChange}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-r-none rounded-l-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
                        placeholder="https://instagram.com/yourprofile"
                      />
                      <div className="px-3 py-2 bg-gray-100 border border-r-gray-300 border-l-0 border-y-gray-300 rounded-l-none rounded-r-md flex items-center">
                        <FaInstagram className="text-pink-500" />
                      </div>
                    </div>
                  </div>

                  <div>
                    <label htmlFor="youtube" className="block text-sm font-medium text-gray-700 mb-1">يوتيوب</label>
                    <div className="flex">
                      <input
                        id="youtube"
                        name="youtube"
                        type="text"
                        value={settings.socialLinks.youtube}
                        onChange={handleSocialChange}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-r-none rounded-l-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
                        placeholder="https://youtube.com/c/yourchannel"
                      />
                      <div className="px-3 py-2 bg-gray-100 border border-r-gray-300 border-l-0 border-y-gray-300 rounded-l-none rounded-r-md flex items-center">
                        <FaYoutube className="text-red-600" />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="contact" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FaPhone className="text-[var(--primary-color)]" />
                    <span>معلومات الاتصال</span>
                  </CardTitle>
                  <CardDescription>
                    إدارة معلومات الاتصال التي تظهر في الموقع
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">البريد الإلكتروني</label>
                    <div className="flex">
                      <input
                        id="email"
                        name="email"
                        type="email"
                        value={settings.contactInfo.email}
                        onChange={handleContactChange}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-r-none rounded-l-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
                        placeholder="<EMAIL>"
                      />
                      <div className="px-3 py-2 bg-gray-100 border border-r-gray-300 border-l-0 border-y-gray-300 rounded-l-none rounded-r-md flex items-center">
                        <FaEnvelope className="text-gray-500" />
                      </div>
                    </div>
                  </div>

                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">رقم الهاتف</label>
                    <div className="flex">
                      <input
                        id="phone"
                        name="phone"
                        type="tel"
                        value={settings.contactInfo.phone}
                        onChange={handleContactChange}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-r-none rounded-l-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
                        placeholder="+123456789"
                      />
                      <div className="px-3 py-2 bg-gray-100 border border-r-gray-300 border-l-0 border-y-gray-300 rounded-l-none rounded-r-md flex items-center">
                        <FaPhone className="text-gray-500" />
                      </div>
                    </div>
                  </div>

                  <div>
                    <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">العنوان</label>
                    <div className="flex">
                      <textarea
                        id="address"
                        name="address"
                        value={settings.contactInfo.address}
                        onChange={handleContactChange}
                        rows={3}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
                        placeholder="العنوان الكامل"
                      />
                    </div>
                  </div>

                  {/* قسم معلومات التبرع */}
                  <div className="border-t pt-6 mt-6">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                      <FaHandHoldingHeart className="ml-2 text-[var(--primary-color)]" />
                      معلومات التبرع
                    </h3>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* رقم الهاتف الأول للتبرعات */}
                      <div>
                        <label htmlFor="phone1" className="block text-sm font-medium text-gray-700 mb-1">
                          رقم الهاتف الأول للتبرعات
                        </label>
                        <input
                          id="phone1"
                          name="phone1"
                          type="tel"
                          value={settings.contactInfo.donationInfo?.phone1 || ''}
                          onChange={handleDonationInfoChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
                          placeholder="+213 123 456 789"
                        />
                      </div>

                      {/* رقم الهاتف الثاني للتبرعات */}
                      <div>
                        <label htmlFor="phone2" className="block text-sm font-medium text-gray-700 mb-1">
                          رقم الهاتف الثاني (اختياري)
                        </label>
                        <input
                          id="phone2"
                          name="phone2"
                          type="tel"
                          value={settings.contactInfo.donationInfo?.phone2 || ''}
                          onChange={handleDonationInfoChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
                          placeholder="+213 987 654 321"
                        />
                      </div>

                      {/* حساب CCP */}
                      <div>
                        <label htmlFor="ccpAccount" className="block text-sm font-medium text-gray-700 mb-1">
                          حساب CCP
                        </label>
                        <input
                          id="ccpAccount"
                          name="ccpAccount"
                          type="text"
                          value={settings.contactInfo.donationInfo?.ccpAccount || ''}
                          onChange={handleDonationInfoChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
                          placeholder="**********"
                        />
                      </div>

                      {/* حساب CPA */}
                      <div>
                        <label htmlFor="cpaAccount" className="block text-sm font-medium text-gray-700 mb-1">
                          حساب CPA
                        </label>
                        <input
                          id="cpaAccount"
                          name="cpaAccount"
                          type="text"
                          value={settings.contactInfo.donationInfo?.cpaAccount || ''}
                          onChange={handleDonationInfoChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
                          placeholder="**********"
                        />
                      </div>

                      {/* حساب BDR */}
                      <div>
                        <label htmlFor="bdrAccount" className="block text-sm font-medium text-gray-700 mb-1">
                          حساب BDR
                        </label>
                        <input
                          id="bdrAccount"
                          name="bdrAccount"
                          type="text"
                          value={settings.contactInfo.donationInfo?.bdrAccount || ''}
                          onChange={handleDonationInfoChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
                          placeholder="**********"
                        />
                      </div>
                    </div>

                    {/* وصف التبرعات */}
                    <div className="mt-4">
                      <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                        وصف إضافي للتبرعات (اختياري)
                      </label>
                      <textarea
                        id="description"
                        name="description"
                        value={settings.contactInfo.donationInfo?.description || ''}
                        onChange={handleDonationInfoChange}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
                        placeholder="يمكنكم التبرع من خلال الحسابات التالية أو التواصل معنا هاتفياً"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="page-backgrounds" className="space-y-6">
              {/* إدارة خلفيات الصفحات العامة */}
              <PageBackgroundsManager />
            </TabsContent>

          </Tabs>
        </div>
      </div>

      {/* زر مختبر التباين */}
      <button
        onClick={() => setShowContrastTester(!showContrastTester)}
        className="fixed bottom-4 right-4 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors z-40"
        title="مختبر التباين"
      >
        🎨
      </button>

      {/* مختبر التباين */}
      <ColorContrastTester isVisible={showContrastTester} />

      </section>
    </OptimizedProtectedRoute>
  );
};

export default AdminSetupPage;